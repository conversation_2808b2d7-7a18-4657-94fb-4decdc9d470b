import pandas as pd
import numpy as np

def analyze_volume_price(data: pd.DataFrame) -> list:
    """
    分析量价关系，识别关键形态。
    """
    analysis_results = []
    # Calculate average volume
    avg_volume = data['volume'].mean()
    
    # Identify significant volume bars (e.g., > 1.5x average)
    significant_volume_bars = data[data['volume'] > avg_volume * 1.5]

    for index, row in significant_volume_bars.iterrows():
        price_change = row['close'] - row['open']
        if price_change > 0:
            analysis_results.append(f"{row['datetime']}: 放量上涨")
        else:
            analysis_results.append(f"{row['datetime']}: 放量下跌")
            
    # Analyze recent trend
    recent_data = data.tail(12) # Analyze last few hours (e.g., 3 hours for 15-min bars)
    price_trend = "上涨" if recent_data['close'].iloc[-1] > recent_data['close'].iloc[0] else "下跌"
    volume_trend = "放量" if recent_data['volume'].iloc[-1] > avg_volume else "缩量"
    
    analysis_results.append(f"近期趋势: {volume_trend}{price_trend}")

    return analysis_results

def identify_key_behaviors(data: pd.DataFrame) -> list:
    """
    识别关键价格行为，如长上/下影线。
    """
    behaviors = []
    
    for index, row in data.iterrows():
        body_size = abs(row['close'] - row['open'])
        upper_shadow = row['high'] - max(row['open'], row['close'])
        lower_shadow = min(row['open'], row['close']) - row['low']
        
        # Threshold for a "long" shadow (e.g., shadow is larger than the body)
        if upper_shadow > body_size * 1.5 and upper_shadow > (row['high'] - row['low']) * 0.4:
            behaviors.append(f"{row['datetime']}: 出现长上影线，抛压较重")
        
        if lower_shadow > body_size * 1.5 and lower_shadow > (row['high'] - row['low']) * 0.4:
            behaviors.append(f"{row['datetime']}: 出现长下影线，承接力强")
            
    # Return only the most recent behaviors to avoid noise
    return behaviors[-5:]


def analyze_micro(minute_data: pd.DataFrame) -> dict:
    """
    执行完整的微观分析。
    """
    if minute_data.empty or len(minute_data) < 20:
        return {"error": "分钟线数据不足以进行微观分析"}

    volume_price_analysis = analyze_volume_price(minute_data)
    key_behaviors = identify_key_behaviors(minute_data)

    # Summarize sentiment
    sentiment = "中性"
    positive_signals = sum(1 for b in key_behaviors if "承接力强" in b) + sum(1 for a in volume_price_analysis if "放量上涨" in a)
    negative_signals = sum(1 for b in key_behaviors if "抛压较重" in b) + sum(1 for a in volume_price_analysis if "放量下跌" in a)

    if positive_signals > negative_signals:
        sentiment = "偏向积极"
    elif negative_signals > positive_signals:
        sentiment = "偏向谨慎"

    return {
        "sentiment": sentiment,
        "volume_price_analysis": volume_price_analysis,
        "key_behaviors": key_behaviors
    }

if __name__ == '__main__':
    # Example usage with dummy data
    dates = pd.to_datetime(pd.date_range(start='2023-05-24 09:30', periods=100, freq='15min'))
    price_data = 50 + np.random.randn(100).cumsum()
    dummy_data = pd.DataFrame({
        'datetime': dates,
        'open': price_data - np.random.rand(100) * 0.5,
        'high': price_data + np.random.rand(100) * 0.5,
        'low': price_data - np.random.rand(100) * 0.5,
        'close': price_data,
        'volume': np.random.randint(1000, 5000, 100)
    })
    # Add a long shadow candle for testing
    dummy_data.loc[dummy_data.index[-1], 'high'] = dummy_data.loc[dummy_data.index[-1], 'close'] + 2
    dummy_data.loc[dummy_data.index[-2], 'low'] = dummy_data.loc[dummy_data.index[-2], 'close'] - 2


    micro_analysis_result = analyze_micro(dummy_data)
    print("--- 微观分析结果 ---")
    print(micro_analysis_result)
