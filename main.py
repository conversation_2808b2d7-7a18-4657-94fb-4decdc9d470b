import sys
import config
import data_fetcher
import macro_analysis
import micro_analysis
import reporter

def main(stock_code: str):
    """
    主函数，执行完整的股票分析流程。
    """
    print(f"--- 开始为股票 {stock_code} 生成分析报告 ---")

    # 1. 数据获取
    print("步骤 1/4: 获取数据...")
    daily_data = data_fetcher.get_daily_data(stock_code)
    minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=7)

    if daily_data.empty or minute_data.empty:
        print("错误：无法获取到足够的股票数据，程序终止。")
        return

    # 2. 宏观分析
    print("步骤 2/4: 进行宏观分析...")
    macro_result = macro_analysis.analyze_macro(daily_data)
    if "error" in macro_result:
        print(f"宏观分析出错: {macro_result['error']}")
        return
    print(f"  宏观趋势: {macro_result['trend']}")

    # 3. 微观分析
    print("步骤 3/4: 进行微观分析...")
    micro_result = micro_analysis.analyze_micro(minute_data)
    if "error" in micro_result:
        print(f"微观分析出错: {micro_result['error']}")
        return
    print(f"  微观情绪: {micro_result['sentiment']}")

    # 4. 生成报告
    print("步骤 4/4: 生成最终报告...")
    final_report = reporter.generate_report(stock_code, macro_result, micro_result)

    print("\n--- 分析报告生成完毕 ---")
    print(final_report)
    
    # Save report to a file
    with open(f"stock_report_{stock_code}.md", "w", encoding="utf-8") as f:
        f.write(final_report)
    print(f"\n报告已保存至 stock_report_{stock_code}.md")


if __name__ == "__main__":
    # You can run the script with a stock code as an argument
    # e.g., python main.py 600519.SH
    if len(sys.argv) > 1:
        stock_to_analyze = sys.argv[1]
    else:
        stock_to_analyze = config.STOCK_CODE
    
    main(stock_to_analyze)
