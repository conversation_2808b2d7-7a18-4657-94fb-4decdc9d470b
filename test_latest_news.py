#!/usr/bin/env python3
"""
测试最新信息功能
"""

import config
import reporter
import data_fetcher
import macro_analysis
import feature_extractor

def test_with_latest_news():
    """
    测试有最新信息的情况
    """
    stock_code = "AAPL"
    
    print(f"=== 测试 {stock_code} 有最新信息的情况 ===")
    
    # 模拟最新信息
    latest_news = """
- Apple发布Q1 2025财报，营收超预期达到$125B，同比增长8%
- iPhone 17销量强劲，中国市场恢复增长
- 分析师摩根士丹利上调目标价至$220，维持买入评级
- 盘前交易上涨3.2%，成交量放大
- CEO库克在财报会议上宣布AI功能将在春季大幅升级
- 服务业务营收创历史新高，达到$28B
"""
    
    # 获取基础数据
    print("正在获取股票数据...")
    daily_data = data_fetcher.get_daily_data(stock_code, days=180)
    minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=7)
    
    if daily_data.empty or minute_data.empty:
        print("错误：无法获取股票数据")
        return
    
    # 进行分析
    macro_result = macro_analysis.analyze_macro(daily_data)
    abnormal_features = feature_extractor.extract_abnormal_features(daily_data, minute_data)
    
    # 生成报告
    print("正在生成报告（有最新信息）...")
    report = reporter.generate_gemini_report(stock_code, macro_result, abnormal_features, latest_news)
    
    print("\n" + "="*80)
    print("有最新信息的报告")
    print("="*80)
    print(report)
    print("="*80)

def test_without_latest_news():
    """
    测试没有最新信息的情况
    """
    stock_code = "AAPL"
    
    print(f"\n=== 测试 {stock_code} 没有最新信息的情况 ===")
    
    # 获取基础数据
    daily_data = data_fetcher.get_daily_data(stock_code, days=180)
    minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=7)
    
    if daily_data.empty or minute_data.empty:
        print("错误：无法获取股票数据")
        return
    
    # 进行分析
    macro_result = macro_analysis.analyze_macro(daily_data)
    abnormal_features = feature_extractor.extract_abnormal_features(daily_data, minute_data)
    
    # 生成报告（不提供最新信息）
    print("正在生成报告（无最新信息）...")
    report = reporter.generate_gemini_report(stock_code, macro_result, abnormal_features, "")
    
    print("\n" + "="*80)
    print("无最新信息的报告")
    print("="*80)
    print(report)
    print("="*80)

if __name__ == "__main__":
    if not config.GEMINI_API_KEY:
        print("错误：未配置GEMINI_API_KEY")
    else:
        # 先测试没有最新信息的情况
        test_without_latest_news()
        
        # 再测试有最新信息的情况
        test_with_latest_news()
