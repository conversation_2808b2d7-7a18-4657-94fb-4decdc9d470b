#!/usr/bin/env python3
"""
测试Gemini搜索功能的脚本
"""

import config
import reporter
import data_fetcher
import macro_analysis
import feature_extractor

def test_gemini_search():
    """
    测试Gemini的网络搜索功能
    """
    # 测试美股代码
    stock_code = "AAPL"
    
    print(f"=== 测试 {stock_code} 的Gemini搜索功能 ===")
    
    # 检查API密钥
    if not config.GEMINI_API_KEY:
        print("错误：未配置GEMINI_API_KEY，请在.env文件中设置")
        return
    
    print("✓ Gemini API密钥已配置")
    
    # 获取基础数据
    print("正在获取股票数据...")
    daily_data = data_fetcher.get_daily_data(stock_code, days=30)
    minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=3)
    
    if daily_data.empty or minute_data.empty:
        print("错误：无法获取股票数据")
        return
    
    print(f"✓ 成功获取 {len(daily_data)} 天日线数据和 {len(minute_data)} 条分钟线数据")
    
    # 进行宏观分析
    print("正在进行宏观分析...")
    macro_result = macro_analysis.analyze_macro(daily_data)
    if "error" in macro_result:
        print(f"宏观分析出错: {macro_result['error']}")
        return
    
    print(f"✓ 宏观分析完成，趋势: {macro_result['trend']}")
    
    # 提取异常特征
    print("正在提取异常特征...")
    abnormal_features = feature_extractor.extract_abnormal_features(daily_data, minute_data)
    if "error" in abnormal_features:
        print(f"特征提取出错: {abnormal_features['error']}")
        return
    
    print("✓ 异常特征提取完成")
    
    # 生成Gemini报告（带搜索功能）
    print("正在调用Gemini进行网络搜索和分析...")
    print("注意：这可能需要30-60秒，因为需要进行网络搜索...")
    
    try:
        report = reporter.generate_gemini_report(stock_code, macro_result, abnormal_features)
        
        print("\n" + "="*80)
        print("GEMINI 搜索分析报告")
        print("="*80)
        print(report)
        print("="*80)
        
        # 保存报告
        filename = f"gemini_search_test_{stock_code}.md"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n✓ 报告已保存到 {filename}")
        
        # 检查报告是否包含搜索结果的迹象
        search_indicators = [
            "24小时", "盘前", "盘后", "最新", "突发", 
            "今日", "当日", "breaking", "news", "latest"
        ]
        
        found_indicators = [indicator for indicator in search_indicators if indicator.lower() in report.lower()]
        
        if found_indicators:
            print(f"✓ 报告似乎包含了搜索结果，发现关键词: {found_indicators}")
        else:
            print("⚠ 报告可能没有包含搜索结果，请检查网络连接和API配置")
            
    except Exception as e:
        print(f"生成报告时出错: {e}")

if __name__ == "__main__":
    test_gemini_search()
