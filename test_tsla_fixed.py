#!/usr/bin/env python3
"""
测试修复后的TSLA分析
"""

import config
import reporter
import data_fetcher
import macro_analysis
import feature_extractor

def test_tsla_analysis():
    """
    测试修复后的TSLA分析
    """
    stock_code = "TSLA"
    
    print(f"=== 测试修复后的 {stock_code} 分析 ===")
    
    # 模拟TSLA的最新信息
    latest_news = """
- 特斯拉Q3财报超预期，汽车交付量达到48.5万辆，同比增长35%
- Cybertruck生产进展顺利，预计Q4开始大规模交付
- 马斯克在财报会议上确认FSD V13版本将在年底发布
- 中国市场销量强劲，上海工厂产能利用率达到95%
- 分析师韦德布什上调目标价至$400，维持买入评级
- 盘前交易上涨2.8%，成交量较平时增加40%
- 能源业务营收创新高，储能系统需求旺盛
"""
    
    # 获取数据
    print("正在获取股票数据...")
    daily_data = data_fetcher.get_daily_data(stock_code, days=180)
    minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=7)
    
    if daily_data.empty or minute_data.empty:
        print("错误：无法获取股票数据")
        return
    
    current_price = daily_data['close'].iloc[-1]
    print(f"当前价格: ${current_price:.2f}")
    
    # 进行分析
    print("正在进行宏观分析...")
    macro_result = macro_analysis.analyze_macro(daily_data)
    print("宏观分析结果:")
    for key, value in macro_result.items():
        print(f"  {key}: {value}")
    
    print("正在提取异常特征...")
    abnormal_features = feature_extractor.extract_abnormal_features(daily_data, minute_data)
    
    # 验证数据合理性
    if 'support' in macro_result and 'resistance' in macro_result:
        support_diff = abs(current_price - macro_result['support']) / current_price * 100
        resistance_diff = abs(macro_result['resistance'] - current_price) / current_price * 100
        
        print(f"\n数据合理性检查:")
        print(f"  支撑位距离: {support_diff:.1f}%")
        print(f"  阻力位距离: {resistance_diff:.1f}%")
        
        if support_diff > 20 or resistance_diff > 20:
            print("  ⚠️ 警告：支撑阻力位距离过远，可能不合理")
        else:
            print("  ✅ 支撑阻力位距离合理")
    
    # 生成报告
    print("\n正在生成AI分析报告...")
    try:
        report = reporter.generate_gemini_report(stock_code, macro_result, abnormal_features, latest_news)
        
        print("\n" + "="*80)
        print("修复后的TSLA分析报告")
        print("="*80)
        print(report)
        print("="*80)
        
        # 保存报告
        filename = f"tsla_fixed_analysis.md"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n✅ 报告已保存到 {filename}")
        
    except Exception as e:
        print(f"生成报告时出错: {e}")

if __name__ == "__main__":
    if not config.GEMINI_API_KEY:
        print("错误：未配置GEMINI_API_KEY")
    else:
        test_tsla_analysis()
