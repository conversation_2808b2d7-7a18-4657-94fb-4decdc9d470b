import pandas as pd
import numpy as np
from arch import arch_model

def extract_abnormal_features(df_daily: pd.DataFrame, df_minute: pd.DataFrame) -> dict:
    """
    提取揭示市场异常行为的量化特征，用于深度分析。
    返回一个包含异常特征字典。
    """
    features = {}

    if df_daily.empty or df_minute.empty:
        return {"error": "输入数据为空, 无法提取特征"}

    # 1. 波动聚集效应 (GARCH模型)
    try:
        returns = df_daily['close'].pct_change().dropna() * 100
        if len(returns) > 20:
            model = arch_model(returns, vol='Garch', p=1, q=1, rescale=False)
            res = model.fit(disp='off')
            features['vol_clustering'] = round(res.params['omega'], 4)
        else:
            features['vol_clustering'] = 'N/A (数据不足)'
    except Exception as e:
        features['vol_clustering'] = f'计算错误: {e}'

    # 2. 关键价格区的成交量异常
    try:
        support_level = df_daily['close'].quantile(0.3)
        resistance_level = df_daily['close'].quantile(0.7)
        global_avg_vol = df_daily['volume'].mean()

        support_vols = df_daily[df_daily['close'] <= support_level]['volume']
        resistance_vols = df_daily[df_daily['close'] >= resistance_level]['volume']

        features['support_vol_ratio'] = round(support_vols.mean() / global_avg_vol, 2) if not support_vols.empty else 1.0
        features['resistance_vol_ratio'] = round(resistance_vols.mean() / global_avg_vol, 2) if not resistance_vols.empty else 1.0
    except Exception as e:
        features['support_vol_ratio'] = f'计算错误: {e}'
        features['resistance_vol_ratio'] = f'计算错误: {e}'

    # 3. 大单足迹浓度
    try:
        total_volume = df_minute['volume'].sum()
        top_10_percent_volume_bars = df_minute.nlargest(int(len(df_minute) * 0.1), 'volume')
        large_order_volume = top_10_percent_volume_bars['volume'].sum()
        features['large_order_ratio'] = round(large_order_volume / total_volume, 2) if total_volume > 0 else 0.0
    except Exception as e:
        features['large_order_ratio'] = f'计算错误: {e}'

    # 4. 多空力量不对称性
    try:
        df_minute['price_change'] = df_minute['close'] - df_minute['open']
        up_bars = df_minute[df_minute['price_change'] > 0]
        down_bars = df_minute[df_minute['price_change'] < 0]
        
        avg_up_volume = up_bars['volume'].mean()
        avg_down_volume = down_bars['volume'].mean()

        features['asymmetry_ratio'] = round(avg_up_volume / avg_down_volume, 2) if avg_down_volume > 0 else np.inf
    except Exception as e:
        features['asymmetry_ratio'] = f'计算错误: {e}'

    # 5. 盘中极端波动（闪崩/急拉）预警
    try:
        df_minute['range_pct'] = (df_minute['high'] - df_minute['low']) / df_minute['close']
        features['flash_crash_risk'] = 1 if (df_minute['range_pct'] > 0.05).any() else 0
    except Exception as e:
        features['flash_crash_risk'] = f'计算错误: {e}'
        
    return features

if __name__ == '__main__':
    # Create dummy data for testing
    dates_daily = pd.to_datetime(pd.date_range(start='2023-01-01', periods=200))
    price_data_daily = 100 + np.random.randn(200).cumsum()
    dummy_daily = pd.DataFrame({
        'date': dates_daily, 'open': price_data_daily, 'high': price_data_daily + 1,
        'low': price_data_daily - 1, 'close': price_data_daily, 'volume': np.random.randint(1e6, 5e6, 200)
    })

    dates_minute = pd.to_datetime(pd.date_range(start='2023-08-01 09:30', periods=1000, freq='15min'))
    price_data_minute = 110 + np.random.randn(1000).cumsum() * 0.1
    dummy_minute = pd.DataFrame({
        'datetime': dates_minute, 'open': price_data_minute, 'high': price_data_minute + 0.1,
        'low': price_data_minute - 0.1, 'close': price_data_minute, 'volume': np.random.randint(1e4, 5e4, 1000)
    })

    abnormal_features = extract_abnormal_features(dummy_daily, dummy_minute)
    print("--- 提取的异常特征 ---")
    import json
    print(json.dumps(abnormal_features, indent=2))
