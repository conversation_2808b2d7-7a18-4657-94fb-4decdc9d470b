import akshare as ak
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta

def _is_us_stock(stock_code: str) -> bool:
    """
    判断股票代码是否为美股。
    简单规则：不包含.SH或.SZ，且全为字母。
    """
    return '.' not in stock_code and stock_code.isalpha()

def get_daily_data(stock_code: str, days: int = 180) -> pd.DataFrame:
    """
    获取指定股票的日线数据，自动适配A股和美股。
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days * 1.5)

    if _is_us_stock(stock_code):
        try:
            ticker = yf.Ticker(stock_code)
            df = ticker.history(start=start_date, end=end_date, interval="1d")
            if df.empty:
                print(f"无法获取 {stock_code} 的美股日线数据。")
                return pd.DataFrame()
            df.reset_index(inplace=True)
            df.rename(columns={'Date': 'date', 'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)
            return df[['date', 'open', 'high', 'low', 'close', 'volume']].tail(days)
        except Exception as e:
            print(f"使用yfinance获取日线数据时出错: {e}")
            return pd.DataFrame()
    else:
        # A-share logic
        code_for_ak = stock_code.split('.')[0] if '.' in stock_code else stock_code
        try:
            df = ak.stock_zh_a_hist(symbol=code_for_ak, period="daily", start_date=start_date.strftime('%Y%m%d'), end_date=end_date.strftime('%Y%m%d'), adjust="qfq")
            if df.empty:
                print(f"无法获取 {stock_code} 的A股日线数据。")
                return pd.DataFrame()
            df.rename(columns={'日期': 'date', '开盘': 'open', '最高': 'high', '最低': 'low', '收盘': 'close', '成交量': 'volume'}, inplace=True)
            df['date'] = pd.to_datetime(df['date'])
            return df[['date', 'open', 'high', 'low', 'close', 'volume']].tail(days)
        except Exception as e:
            print(f"使用akshare获取日线数据时出错: {e}")
            return pd.DataFrame()

def get_minute_data(stock_code: str, period: str = '15', days: int = 7) -> pd.DataFrame:
    """
    获取指定股票的分钟线数据，自动适配A股和美股。
    注意: yfinance的分钟数据最多只能获取最近7天。
    """
    if _is_us_stock(stock_code):
        try:
            ticker = yf.Ticker(stock_code)
            # yfinance uses '15m' for 15-minute interval
            df = ticker.history(period=f"{days}d", interval=f"{period}m")
            if df.empty:
                print(f"无法获取 {stock_code} 的美股分钟线数据。")
                return pd.DataFrame()
            df.reset_index(inplace=True)
            df.rename(columns={'Datetime': 'datetime', 'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)
            return df[['datetime', 'open', 'high', 'low', 'close', 'volume']]
        except Exception as e:
            print(f"使用yfinance获取分钟线数据时出错: {e}")
            return pd.DataFrame()
    else:
        # A-share logic
        code_for_ak = stock_code.split('.')[0] if '.' in stock_code else stock_code
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        try:
            df = ak.stock_zh_a_hist_min_em(symbol=code_for_ak, start_date=start_date.strftime('%Y-%m-%d %H:%M:%S'), end_date=end_date.strftime('%Y-%m-%d %H:%M:%S'), period=period, adjust='qfq')
            if df.empty:
                print(f"无法获取 {stock_code} 的A股分钟线数据。")
                return pd.DataFrame()
            df.rename(columns={'时间': 'datetime', '开盘': 'open', '最高': 'high', '最低': 'low', '收盘': 'close', '成交量': 'volume'}, inplace=True)
            df['datetime'] = pd.to_datetime(df['datetime'])
            return df[['datetime', 'open', 'high', 'low', 'close', 'volume']]
        except Exception as e:
            print(f"使用akshare获取分钟线数据时出错: {e}")
            return pd.DataFrame()

if __name__ == '__main__':
    # Test A-share
    print("--- 测试A股数据获取 (000001.SZ) ---")
    daily_data_cn = get_daily_data("000001.SZ")
    if not daily_data_cn.empty:
        print(daily_data_cn.tail())
    
    # Test US stock
    print("\n--- 测试美股数据获取 (AAPL) ---")
    daily_data_us = get_daily_data("AAPL")
    if not daily_data_us.empty:
        print(daily_data_us.tail())
