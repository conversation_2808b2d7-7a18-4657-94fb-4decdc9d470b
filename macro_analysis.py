import pandas as pd
import numpy as np

def calculate_ma(data: pd.DataFrame, short_window: int = 20, long_window: int = 60) -> pd.DataFrame:
    """
    计算短期和长期移动平均线。
    """
    data[f'ma_{short_window}'] = data['close'].rolling(window=short_window).mean()
    data[f'ma_{long_window}'] = data['close'].rolling(window=long_window).mean()
    return data

def identify_trend(data: pd.DataFrame, short_window: int = 20, long_window: int = 60) -> str:
    """
    通过MA和价格位置判断宏观趋势。
    """
    last_row = data.iloc[-1]
    price = last_row['close']
    ma_short = last_row[f'ma_{short_window}']
    ma_long = last_row[f'ma_{long_window}']

    if pd.isna(ma_short) or pd.isna(ma_long):
        return "数据不足，无法判断趋势"

    if price > ma_short > ma_long:
        # 检查金叉是否发生在近期
        prev_row = data.iloc[-2]
        if prev_row[f'ma_{short_window}'] <= prev_row[f'ma_{long_window}'] and ma_short > ma_long:
            return "上升趋势（金叉）"
        return "上升趋势"
    elif price < ma_short < ma_long:
        # 检查死叉是否发生在近期
        prev_row = data.iloc[-2]
        if prev_row[f'ma_{short_window}'] >= prev_row[f'ma_{long_window}'] and ma_short < ma_long:
            return "下降趋势（死叉）"
        return "下降趋势"
    else:
        return "震荡盘整"

def find_support_resistance(data: pd.DataFrame, window: int = 180) -> dict:
    """
    识别近期的支撑位和阻力位。
    """
    recent_data = data.tail(window)
    support = recent_data['low'].min()
    resistance = recent_data['high'].max()
    
    # A more sophisticated way could be finding local minima/maxima
    # For simplicity, we use the absolute min/max over the period.
    
    return {'support': support, 'resistance': resistance}

def calculate_atr(data: pd.DataFrame, period: int = 14) -> float:
    """
    计算ATR（平均真实波幅）。
    """
    high_low = data['high'] - data['low']
    high_close = np.abs(data['high'] - data['close'].shift())
    low_close = np.abs(data['low'] - data['close'].shift())
    
    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = tr.rolling(window=period).mean().iloc[-1]
    
    return round(atr, 2) if not pd.isna(atr) else 0.0

def analyze_macro(daily_data: pd.DataFrame) -> dict:
    """
    执行完整的宏观分析。
    """
    if daily_data.empty or len(daily_data) < 60:
        return {"error": "日线数据不足以进行宏观分析"}

    data_with_ma = calculate_ma(daily_data.copy())
    trend = identify_trend(data_with_ma)
    levels = find_support_resistance(data_with_ma)
    atr = calculate_atr(data_with_ma)

    return {
        "trend": trend,
        "support": levels['support'],
        "resistance": levels['resistance'],
        "atr": atr
    }

if __name__ == '__main__':
    # Example usage with dummy data
    dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=200))
    price_data = 100 + np.random.randn(200).cumsum()
    dummy_data = pd.DataFrame({
        'date': dates,
        'open': price_data - 1,
        'high': price_data + 2,
        'low': price_data - 2,
        'close': price_data,
        'volume': np.random.randint(10000, 50000, 200)
    })

    macro_analysis_result = analyze_macro(dummy_data)
    print("--- 宏观分析结果 ---")
    print(macro_analysis_result)
