import pandas as pd
import numpy as np

def calculate_ma(data: pd.DataFrame, short_window: int = 20, long_window: int = 60) -> pd.DataFrame:
    """
    计算短期和长期移动平均线。
    """
    data[f'ma_{short_window}'] = data['close'].rolling(window=short_window).mean()
    data[f'ma_{long_window}'] = data['close'].rolling(window=long_window).mean()
    return data

def identify_trend(data: pd.DataFrame, short_window: int = 20, long_window: int = 60) -> str:
    """
    通过MA和价格位置判断宏观趋势。
    """
    last_row = data.iloc[-1]
    price = last_row['close']
    ma_short = last_row[f'ma_{short_window}']
    ma_long = last_row[f'ma_{long_window}']

    if pd.isna(ma_short) or pd.isna(ma_long):
        return "数据不足，无法判断趋势"

    if price > ma_short > ma_long:
        # 检查金叉是否发生在近期
        prev_row = data.iloc[-2]
        if prev_row[f'ma_{short_window}'] <= prev_row[f'ma_{long_window}'] and ma_short > ma_long:
            return "上升趋势（金叉）"
        return "上升趋势"
    elif price < ma_short < ma_long:
        # 检查死叉是否发生在近期
        prev_row = data.iloc[-2]
        if prev_row[f'ma_{short_window}'] >= prev_row[f'ma_{long_window}'] and ma_short < ma_long:
            return "下降趋势（死叉）"
        return "下降趋势"
    else:
        return "震荡盘整"

def find_support_resistance(data: pd.DataFrame, window: int = 60) -> dict:
    """
    识别近期的有效支撑位和阻力位。
    使用更合理的技术分析方法，而不是简单的最高最低点。
    """
    # 使用最近60天的数据，而不是180天
    recent_data = data.tail(window).copy()
    current_price = recent_data['close'].iloc[-1]

    # 方法1：寻找近期的关键价格区域（局部极值）
    # 计算20日高点和低点的局部极值
    highs = recent_data['high'].rolling(window=5, center=True).max()
    lows = recent_data['low'].rolling(window=5, center=True).min()

    # 找到真正的局部高点和低点
    local_highs = recent_data[recent_data['high'] == highs]['high'].dropna()
    local_lows = recent_data[recent_data['low'] == lows]['low'].dropna()

    # 方法2：基于当前价格的相对位置确定支撑阻力
    # 找到当前价格下方最近的支撑位（局部低点）
    support_candidates = local_lows[local_lows < current_price]
    if not support_candidates.empty:
        # 取最接近当前价格的支撑位
        support = support_candidates.max()
    else:
        # 如果没有找到，使用近期低点
        support = recent_data['low'].tail(20).min()

    # 找到当前价格上方最近的阻力位（局部高点）
    resistance_candidates = local_highs[local_highs > current_price]
    if not resistance_candidates.empty:
        # 取最接近当前价格的阻力位
        resistance = resistance_candidates.min()
    else:
        # 如果没有找到，使用近期高点
        resistance = recent_data['high'].tail(20).max()

    # 确保支撑阻力位的合理性
    # 支撑位不应该距离当前价格太远（超过15%）
    if (current_price - support) / current_price > 0.15:
        support = current_price * 0.90  # 使用10%作为默认支撑

    # 阻力位不应该距离当前价格太远（超过15%）
    if (resistance - current_price) / current_price > 0.15:
        resistance = current_price * 1.10  # 使用10%作为默认阻力

    return {'support': round(support, 2), 'resistance': round(resistance, 2)}

def calculate_atr(data: pd.DataFrame, period: int = 14) -> float:
    """
    计算ATR（平均真实波幅）。
    """
    high_low = data['high'] - data['low']
    high_close = np.abs(data['high'] - data['close'].shift())
    low_close = np.abs(data['low'] - data['close'].shift())
    
    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = tr.rolling(window=period).mean().iloc[-1]
    
    return round(atr, 2) if not pd.isna(atr) else 0.0

def analyze_macro(daily_data: pd.DataFrame) -> dict:
    """
    执行完整的宏观分析。
    """
    if daily_data.empty or len(daily_data) < 60:
        return {"error": "日线数据不足以进行宏观分析"}

    data_with_ma = calculate_ma(daily_data.copy())
    trend = identify_trend(data_with_ma)
    levels = find_support_resistance(data_with_ma)
    atr = calculate_atr(data_with_ma)

    return {
        "trend": trend,
        "support": levels['support'],
        "resistance": levels['resistance'],
        "atr": atr
    }

if __name__ == '__main__':
    # Example usage with dummy data
    dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=200))
    price_data = 100 + np.random.randn(200).cumsum()
    dummy_data = pd.DataFrame({
        'date': dates,
        'open': price_data - 1,
        'high': price_data + 2,
        'low': price_data - 2,
        'close': price_data,
        'volume': np.random.randint(10000, 50000, 200)
    })

    macro_analysis_result = analyze_macro(dummy_data)
    print("--- 宏观分析结果 ---")
    print(macro_analysis_result)
