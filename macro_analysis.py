import pandas as pd
import numpy as np

def calculate_ma(data: pd.DataFrame, short_window: int = 20, long_window: int = 60) -> pd.DataFrame:
    """
    计算短期和长期移动平均线。
    """
    data[f'ma_{short_window}'] = data['close'].rolling(window=short_window).mean()
    data[f'ma_{long_window}'] = data['close'].rolling(window=long_window).mean()
    return data

def identify_trend(data: pd.DataFrame, short_window: int = 20, long_window: int = 60) -> str:
    """
    通过MA和价格位置判断宏观趋势。
    """
    last_row = data.iloc[-1]
    price = last_row['close']
    ma_short = last_row[f'ma_{short_window}']
    ma_long = last_row[f'ma_{long_window}']

    if pd.isna(ma_short) or pd.isna(ma_long):
        return "数据不足，无法判断趋势"

    if price > ma_short > ma_long:
        # 检查金叉是否发生在近期
        prev_row = data.iloc[-2]
        if prev_row[f'ma_{short_window}'] <= prev_row[f'ma_{long_window}'] and ma_short > ma_long:
            return "上升趋势（金叉）"
        return "上升趋势"
    elif price < ma_short < ma_long:
        # 检查死叉是否发生在近期
        prev_row = data.iloc[-2]
        if prev_row[f'ma_{short_window}'] >= prev_row[f'ma_{long_window}'] and ma_short < ma_long:
            return "下降趋势（死叉）"
        return "下降趋势"
    else:
        return "震荡盘整"

def calculate_intraday_levels(data: pd.DataFrame) -> dict:
    """
    计算T+0日内交易的关键价位。
    基于近期价格行为和波动率，预测当日可能的价格区间和交易点位。
    """
    # 获取最近的价格数据
    recent_data = data.tail(20).copy()  # 最近20个交易日
    current_price = recent_data['close'].iloc[-1]

    # 计算ATR（平均真实波幅）用于预测当日波动
    atr = calculate_atr(data, period=14)

    # 计算近期波动率
    recent_volatility = recent_data['close'].pct_change().std() * 100

    # 预测当日价格区间
    # 基于ATR和近期波动率
    daily_range_atr = atr * 0.8  # 保守估计，使用80%的ATR
    daily_range_vol = current_price * recent_volatility * 0.01  # 基于波动率

    # 取两者的平均值作为预期日内波动
    expected_daily_range = (daily_range_atr + daily_range_vol) / 2

    # 当日预测价格区间
    intraday_low = current_price - expected_daily_range
    intraday_high = current_price + expected_daily_range

    # T+0交易关键点位
    # 买入区域：当日预测低点附近
    buy_zone_low = intraday_low
    buy_zone_high = current_price - (expected_daily_range * 0.3)

    # 卖出区域：当日预测高点附近
    sell_zone_low = current_price + (expected_daily_range * 0.3)
    sell_zone_high = intraday_high

    # 止损位：基于ATR的2倍
    stop_loss_range = atr * 2

    return {
        'current_price': round(current_price, 2),
        'predicted_low': round(intraday_low, 2),
        'predicted_high': round(intraday_high, 2),
        'buy_zone': [round(buy_zone_low, 2), round(buy_zone_high, 2)],
        'sell_zone': [round(sell_zone_low, 2), round(sell_zone_high, 2)],
        'expected_range': round(expected_daily_range, 2),
        'stop_loss_range': round(stop_loss_range, 2),
        'atr': round(atr, 2),
        'volatility_pct': round(recent_volatility, 2)
    }

def calculate_atr(data: pd.DataFrame, period: int = 14) -> float:
    """
    计算ATR（平均真实波幅）。
    """
    high_low = data['high'] - data['low']
    high_close = np.abs(data['high'] - data['close'].shift())
    low_close = np.abs(data['low'] - data['close'].shift())
    
    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = tr.rolling(window=period).mean().iloc[-1]
    
    return round(atr, 2) if not pd.isna(atr) else 0.0

def analyze_macro(daily_data: pd.DataFrame) -> dict:
    """
    执行完整的T+0日内交易分析。
    """
    if daily_data.empty or len(daily_data) < 20:
        return {"error": "日线数据不足以进行分析（至少需要20天数据）"}

    data_with_ma = calculate_ma(daily_data.copy())
    trend = identify_trend(data_with_ma)
    intraday_levels = calculate_intraday_levels(daily_data)

    # 合并趋势和日内交易数据
    result = {
        "trend": trend,
        **intraday_levels  # 展开日内交易的所有数据
    }

    return result

if __name__ == '__main__':
    # Example usage with dummy data
    dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=200))
    price_data = 100 + np.random.randn(200).cumsum()
    dummy_data = pd.DataFrame({
        'date': dates,
        'open': price_data - 1,
        'high': price_data + 2,
        'low': price_data - 2,
        'close': price_data,
        'volume': np.random.randint(10000, 50000, 200)
    })

    macro_analysis_result = analyze_macro(dummy_data)
    print("--- 宏观分析结果 ---")
    print(macro_analysis_result)
