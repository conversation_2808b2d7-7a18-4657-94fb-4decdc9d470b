import google.generativeai as genai
from http import HTTPStatus
import dashscope
import config
import json
from datetime import datetime
import pytz

def _is_us_stock(stock_code: str) -> bool:
    """
    判断股票代码是否为美股。
    """
    return '.' not in stock_code and stock_code.isalpha()

def generate_qwen_report(stock_code: str, macro_analysis: dict, abnormal_features: dict) -> str:
    """
    使用DashScope qwen-max 和深度分析prompt生成A股报告。
    """
    if not config.DASHSCOPE_API_KEY:
        return "错误：未配置DASHSCOPE_API_KEY。"
    dashscope.api_key = config.DASHSCOPE_API_KEY

    prompt = f"""
# **角色与使命**
你是一位顶尖的量化对冲基金分析师，擅长从市场噪声中解码主力意图和散户情绪。你的使命不是描述趋势，而是**发现异常、揭示风险、提供超越大众的非凡洞察**。

# **输入信息（Raw Data）**
- 股票代码: {stock_code}
- 宏观趋势（传统数据）: {macro_analysis.get('trend', 'N/A')}
- 关键价位（传统数据）: 支撑 {macro_analysis.get('support', 'N/A')}, 阻力 {macro_analysis.get('resistance', 'N/A')}
- 市场异常特征: {json.dumps(abnormal_features, indent=2)}

# **分析框架（你必须遵循的思考流程）**
1.  **异常信号解读:** 解读特征数据，例如：“波动聚集系数高达X，表明市场存在恐慌记忆。”
2.  **博弈行为分析:** 根据“大单浓度”和“关键位成交量”推断主力意图。根据“多空不对称性”判断散户情绪。
3.  **推演与预警:** 推演最可能发生的短期剧情，并指出黑天鹅预警信号。

# **输出格式要求**
**【{stock_code} 深度市场行为分析】**
**🔥 核心异常信号:** [一句话点出最不寻常的发现]
**🧠 行为解码:** 
- 主力意图: [填写]
- 散户情绪: [填写]
- 市场脆弱性: [填写]
**⚡ 推演与预警:** 
- 最可能剧情: [填写]
- 黑天鹅剧本: [填写]
**🎯 超常规操作建议:** 
- 激进策略: [提供具体、反共识的操作]
- 观测点: [指出1-2个必须密切关注的量化信号]
"""
    try:
        response = dashscope.Generation.call(model='qwen-max', prompt=prompt, temperature=0.5)
        return response.output.text if response.status_code == HTTPStatus.OK else f"生成报告时出错: {response.code} - {response.message}"
    except Exception as e:
        return f"生成Qwen报告时出错: {e}"

def generate_gemini_report(stock_code: str, macro_analysis: dict, abnormal_features: dict) -> str:
    """
    使用Gemini Pro并联网搜索，生成美股分析报告。
    """
    if not config.GEMINI_API_KEY:
        return "错误：未配置GEMINI_API_KEY。"
    genai.configure(api_key=config.GEMINI_API_KEY)

    # 获取美国东部时间（纽约时间）
    us_eastern = pytz.timezone('US/Eastern')
    current_us_time = datetime.now(us_eastern)
    current_date = current_us_time.strftime('%Y-%m-%d')
    current_datetime = current_us_time.strftime('%Y-%m-%d %H:%M:%S EST')

    # 启用Google搜索功能 - 使用支持搜索的模型
    model = genai.GenerativeModel('gemini-2.5-flash')

    prompt = f"""
# **角色与使命**
你是一位顶尖的美股分析师，专精于T+0日内交易和波段操作。你的核心优势是**结合实时网络信息（新闻、财报、分析师评级）和量化异常信号**，提供具有前瞻性的交易策略。

# **重要时间信息**
- 当前美国东部时间: {current_datetime}
- 分析日期: {current_date}
- **请确保搜索和分析的是过去24小时内的最新信息**

# **任务指令**
1.  **必须进行实时网络搜索 - 重点关注过去24小时内的信息:**
    - 搜索 "{stock_code} news last 24 hours" 获取过去24小时内的最新新闻
    - 搜索 "{stock_code} breaking news today {current_date}" 获取今日突发新闻
    - 搜索 "{stock_code} after hours trading {current_date}" 获取盘后交易信息
    - 搜索 "{stock_code} premarket {current_date}" 获取盘前交易信息
    - 搜索 "{stock_code} analyst upgrade downgrade {current_date}" 获取最新评级变化
    - 搜索 "{stock_code} earnings guidance update {current_date}" 获取最新业绩指引
    - 搜索任何可能在过去24小时内影响该股票的重大事件、公司公告或市场动态
2.  **数据解读:** 结合搜索到的过去24小时最新信息和以下量化数据进行分析。
3.  **当日T+0策略:** 你的建议必须针对{current_date}这一交易日，充分考虑美股T+0和无涨跌幅限制的特性。

# **输入信息（Raw Data）**
- 股票代码: {stock_code}
- 宏观趋势（量化）: {macro_analysis.get('trend', 'N/A')}
- 关键价位（量化）: 支撑 {macro_analysis.get('support', 'N/A')}, 阻力 {macro_analysis.get('resistance', 'N/A')}
- 市场异常特征（量化）: {json.dumps(abnormal_features, indent=2)}

# **输出格式要求**
**【{stock_code} (US Market) - {current_date} 当日深度分析】**
**🌐 过去24小时实时信息摘要:**
- 24小时内突发新闻: [基于搜索结果，总结过去24小时内影响该股票的关键新闻或事件]
- 盘前/盘后动态: [基于搜索结果，总结盘前盘后交易情况和重要信息]
- 最新评级/业绩变化: [基于搜索结果，总结过去24小时内的分析师评级或业绩指引变化]
- 当前股价状况: [基于搜索结果，说明当前股价水平和最新表现]
**🧠 量化行为解码:**
- 主力/机构意图: [结合量化特征和新闻，判断机构行为]
- 市场情绪: [判断当前市场是risk-on还是risk-off]
**⚡ 推演与预警:**
- 短期关键驱动力: [指出接下来影响股价的核心因素是什么]
- 潜在风险: [基于T+0特性，指出可能的日内大幅波动风险]
**🎯 {current_date} 当日T+0操作建议:**
- 当日核心策略: [基于过去24小时信息，明确建议今日做多、做空或观望]
- 当日关键入场/出场位: [提供具体的价格区间用于今日交易]
- 当日止损策略: [给出严格的止损建议，考虑今日可能的波动]
- 当日重点观测: [指出今日需要特别关注的时间点、新闻发布或价格行为]
- 风险提醒: [基于过去24小时信息，提醒今日可能的突发风险]
"""
    try:
        # 首先尝试使用搜索功能
        try:
            response = model.generate_content(
                prompt,
                tools='google_search_retrieval'
            )
            return response.text
        except Exception as search_error:
            # 如果搜索功能不可用，使用普通模式但在prompt中明确要求基于最新信息
            print(f"搜索功能不可用 ({search_error})，使用普通模式...")

            fallback_prompt = f"""
# **重要说明**
由于技术限制，我无法直接搜索最新信息，但请基于你的训练数据中关于 {stock_code} 的最新可用信息进行分析。

{prompt}

**请在分析中明确说明：**
1. 你使用的是训练数据中的信息，截止到你的知识更新时间
2. 建议用户查看最新的财经新闻网站获取实时信息
3. 提供一般性的分析框架和关注要点
"""

            response = model.generate_content(fallback_prompt)
            return response.text

    except Exception as e:
        return f"生成Gemini报告时出错: {e}"

def generate_report(stock_code: str, macro_analysis: dict, abnormal_features: dict) -> str:
    """
    总入口，根据股票市场选择合适的AI模型生成报告。
    """
    if _is_us_stock(stock_code):
        print("检测到美股，使用Gemini Pro进行分析...")
        # For Gemini, we don't need micro_analysis, but the function signature is kept for consistency
        return generate_gemini_report(stock_code, macro_analysis, abnormal_features)
    else:
        print("检测到A股，使用Qwen-Max进行分析...")
        # The original function expected micro_analysis, let's pass an empty dict for now
        return generate_qwen_report(stock_code, macro_analysis, abnormal_features)
