import google.generativeai as genai
from http import HTTPStatus
import dashscope
import config
import json
from datetime import datetime
import pytz

def _is_us_stock(stock_code: str) -> bool:
    """
    判断股票代码是否为美股。
    """
    return '.' not in stock_code and stock_code.isalpha()

def generate_qwen_report(stock_code: str, macro_analysis: dict, abnormal_features: dict) -> str:
    """
    使用DashScope qwen-max 和深度分析prompt生成A股报告。
    """
    if not config.DASHSCOPE_API_KEY:
        return "错误：未配置DASHSCOPE_API_KEY。"
    dashscope.api_key = config.DASHSCOPE_API_KEY

    prompt = f"""
# **角色与使命**
你是一位顶尖的量化对冲基金分析师，擅长从市场噪声中解码主力意图和散户情绪。你的使命不是描述趋势，而是**发现异常、揭示风险、提供超越大众的非凡洞察**。

# **输入信息（Raw Data）**
- 股票代码: {stock_code}
- 宏观趋势（传统数据）: {macro_analysis.get('trend', 'N/A')}
- 关键价位（传统数据）: 支撑 {macro_analysis.get('support', 'N/A')}, 阻力 {macro_analysis.get('resistance', 'N/A')}
- 市场异常特征: {json.dumps(abnormal_features, indent=2)}

# **分析框架（你必须遵循的思考流程）**
1.  **异常信号解读:** 解读特征数据，例如：“波动聚集系数高达X，表明市场存在恐慌记忆。”
2.  **博弈行为分析:** 根据“大单浓度”和“关键位成交量”推断主力意图。根据“多空不对称性”判断散户情绪。
3.  **推演与预警:** 推演最可能发生的短期剧情，并指出黑天鹅预警信号。

# **输出格式要求**
**【{stock_code} 深度市场行为分析】**
**🔥 核心异常信号:** [一句话点出最不寻常的发现]
**🧠 行为解码:** 
- 主力意图: [填写]
- 散户情绪: [填写]
- 市场脆弱性: [填写]
**⚡ 推演与预警:** 
- 最可能剧情: [填写]
- 黑天鹅剧本: [填写]
**🎯 超常规操作建议:** 
- 激进策略: [提供具体、反共识的操作]
- 观测点: [指出1-2个必须密切关注的量化信号]
"""
    try:
        response = dashscope.Generation.call(model='qwen-max', prompt=prompt, temperature=0.5)
        return response.output.text if response.status_code == HTTPStatus.OK else f"生成报告时出错: {response.code} - {response.message}"
    except Exception as e:
        return f"生成Qwen报告时出错: {e}"

def generate_gemini_report(stock_code: str, macro_analysis: dict, abnormal_features: dict, latest_news: str = "") -> str:
    """
    使用Gemini Pro并联网搜索，生成美股分析报告。
    """
    if not config.GEMINI_API_KEY:
        return "错误：未配置GEMINI_API_KEY。"
    genai.configure(api_key=config.GEMINI_API_KEY)

    # 获取美国东部时间（纽约时间）
    us_eastern = pytz.timezone('US/Eastern')
    current_us_time = datetime.now(us_eastern)
    current_date = current_us_time.strftime('%Y-%m-%d')
    current_datetime = current_us_time.strftime('%Y-%m-%d %H:%M:%S EST')

    # 启用Google搜索功能 - 使用gemini-2.5-pro
    model = genai.GenerativeModel('gemini-2.5-pro')

    prompt = f"""
# **角色与使命**
你是一位顶尖的美股分析师，专精于T+0日内交易和波段操作。你的核心优势是**结合实时网络信息（新闻、财报、分析师评级）和量化异常信号**，提供具有前瞻性的交易策略。

# **重要时间信息**
- 当前美国东部时间: {current_datetime}
- 分析日期: {current_date}
- **请确保搜索和分析的是过去24小时内的最新信息**

# **重要分析原则**
**你必须严格遵循以下原则：**
- **只有当用户提供了过去24小时的最新信息时，才进行股票分析**
- **如果用户没有提供最新24小时信息，请明确告知无法进行当日分析**
- **不要基于你的历史训练数据进行股票分析，因为股票市场瞬息万变**
- **所有分析必须基于用户提供的最新信息和量化数据**

# **任务指令**
1.  **信息验证:**
    - 首先检查用户是否提供了过去24小时的最新信息
    - 如果没有提供，请礼貌地说明无法进行当日分析，并建议用户提供最新信息
    - 如果提供了，请重点基于这些最新信息进行分析
2.  **数据解读:** 仅基于用户提供的最新信息和量化数据进行分析，不使用历史训练数据。
3.  **当日T+0策略:** 基于最新信息，针对{current_date}这一交易日提供建议。

# **输入信息（Raw Data）**
- 股票代码: {stock_code}
- 分析时间: {current_datetime}
- 宏观趋势（量化）: {macro_analysis.get('trend', 'N/A')}
- 关键价位（量化）: 支撑 {macro_analysis.get('support', 'N/A')}, 阻力 {macro_analysis.get('resistance', 'N/A')}
- 市场异常特征（量化）: {json.dumps(abnormal_features, indent=2)}
- 用户提供的最新24小时信息: {latest_news if latest_news.strip() else "未提供最新信息"}

# **输出格式要求**

**如果用户没有提供最新24小时信息，请按以下格式回复：**
```
**【{stock_code} - 无法进行当日分析】**

**❌ 分析状态：** 无法进行当日分析

**📋 原因说明：**
由于股票市场瞬息万变，准确的当日分析需要基于过去24小时的最新信息，包括：
- 最新财报或业绩指引
- 分析师评级变化
- 重要新闻事件
- 盘前/盘后交易情况
- 管理层声明或公司公告

**💡 建议：**
请在左侧输入框中提供该股票过去24小时的最新信息，我将基于这些信息结合量化数据为您提供专业的当日交易分析。

您可以从以下渠道获取最新信息：
- Bloomberg, Reuters, Yahoo Finance
- 公司官方网站和投资者关系页面
- SEC filings (Form 8-K, 10-Q等)
- 主要财经媒体的最新报道
```

**如果用户提供了最新24小时信息，请按以下格式分析：**
**【{stock_code} (US Market) - {current_date} 专业分析】**
**🌐 最新信息解读:**
- 信息来源: [基于用户提供的最新24小时信息]
- 关键事件影响: [分析用户提供信息中的关键事件对股价的潜在影响]
- 市场反应预期: [基于最新信息预测市场可能的反应]
**🧠 量化行为解码:**
- 主力/机构意图: [结合量化特征和新闻，判断机构行为]
- 市场情绪: [判断当前市场是risk-on还是risk-off]
**⚡ 推演与预警:**
- 短期关键驱动力: [指出接下来影响股价的核心因素是什么]
- 潜在风险: [基于T+0特性，指出可能的日内大幅波动风险]
**🎯 {current_date} 当日T+0操作建议:**
- 当日核心策略: [基于过去24小时信息，明确建议今日做多、做空或观望]
- 当日关键入场/出场位: [提供具体的价格区间用于今日交易]
- 当日止损策略: [给出严格的止损建议，考虑今日可能的波动]
- 当日重点观测: [指出今日需要特别关注的时间点、新闻发布或价格行为]
- 风险提醒: [基于过去24小时信息，提醒今日可能的突发风险]
"""
    try:
        # 根据是否有最新信息来决定分析方式
        if latest_news.strip():
            # 有最新信息，进行完整分析
            enhanced_prompt = f"""
{prompt}

**分析指导：**
- 用户已提供最新24小时信息，请基于这些信息进行深度分析
- 重点解读最新信息对股价的潜在影响
- 结合量化数据提供专业的当日交易建议
- 分析最新信息中的关键事件和市场反应
"""
        else:
            # 没有最新信息，拒绝分析
            enhanced_prompt = f"""
{prompt}

**重要指示：**
- 用户没有提供过去24小时的最新信息
- 请严格按照输出格式要求，说明无法进行当日分析
- 不要基于历史训练数据进行股票分析
- 明确告知用户需要提供最新信息才能进行分析
"""

        response = model.generate_content(enhanced_prompt)
        return response.text
    except Exception as e:
        return f"生成Gemini报告时出错: {e}"

def generate_report(stock_code: str, macro_analysis: dict, abnormal_features: dict, latest_news: str = "") -> str:
    """
    总入口，根据股票市场选择合适的AI模型生成报告。
    """
    if _is_us_stock(stock_code):
        print("检测到美股，使用Gemini Pro进行分析...")
        return generate_gemini_report(stock_code, macro_analysis, abnormal_features, latest_news)
    else:
        print("检测到A股，使用Qwen-Max进行分析...")
        # A股暂时不支持最新信息功能，保持原有逻辑
        return generate_qwen_report(stock_code, macro_analysis, abnormal_features)
