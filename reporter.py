import google.generativeai as genai
from http import HTTPStatus
import dashscope
import config
import json
from datetime import datetime
import pytz

def _is_us_stock(stock_code: str) -> bool:
    """
    判断股票代码是否为美股。
    """
    return '.' not in stock_code and stock_code.isalpha()

def generate_qwen_report(stock_code: str, macro_analysis: dict, abnormal_features: dict) -> str:
    """
    使用DashScope qwen-max 和深度分析prompt生成A股报告。
    """
    if not config.DASHSCOPE_API_KEY:
        return "错误：未配置DASHSCOPE_API_KEY。"
    dashscope.api_key = config.DASHSCOPE_API_KEY

    prompt = f"""
# **角色与使命**
你是一位顶尖的量化对冲基金分析师，擅长从市场噪声中解码主力意图和散户情绪。你的使命不是描述趋势，而是**发现异常、揭示风险、提供超越大众的非凡洞察**。

# **输入信息（Raw Data）**
- 股票代码: {stock_code}
- 宏观趋势（传统数据）: {macro_analysis.get('trend', 'N/A')}
- 关键价位（传统数据）: 支撑 {macro_analysis.get('support', 'N/A')}, 阻力 {macro_analysis.get('resistance', 'N/A')}
- 市场异常特征: {json.dumps(abnormal_features, indent=2)}

# **分析框架（你必须遵循的思考流程）**
1.  **异常信号解读:** 解读特征数据，例如：“波动聚集系数高达X，表明市场存在恐慌记忆。”
2.  **博弈行为分析:** 根据“大单浓度”和“关键位成交量”推断主力意图。根据“多空不对称性”判断散户情绪。
3.  **推演与预警:** 推演最可能发生的短期剧情，并指出黑天鹅预警信号。

# **输出格式要求**
**【{stock_code} 深度市场行为分析】**
**🔥 核心异常信号:** [一句话点出最不寻常的发现]
**🧠 行为解码:** 
- 主力意图: [填写]
- 散户情绪: [填写]
- 市场脆弱性: [填写]
**⚡ 推演与预警:** 
- 最可能剧情: [填写]
- 黑天鹅剧本: [填写]
**🎯 超常规操作建议:** 
- 激进策略: [提供具体、反共识的操作]
- 观测点: [指出1-2个必须密切关注的量化信号]
"""
    try:
        response = dashscope.Generation.call(model='qwen-max', prompt=prompt, temperature=0.5)
        return response.output.text if response.status_code == HTTPStatus.OK else f"生成报告时出错: {response.code} - {response.message}"
    except Exception as e:
        return f"生成Qwen报告时出错: {e}"

def generate_gemini_report(stock_code: str, macro_analysis: dict, abnormal_features: dict) -> str:
    """
    使用Gemini Pro并联网搜索，生成美股分析报告。
    """
    if not config.GEMINI_API_KEY:
        return "错误：未配置GEMINI_API_KEY。"
    genai.configure(api_key=config.GEMINI_API_KEY)

    # 获取美国东部时间（纽约时间）
    us_eastern = pytz.timezone('US/Eastern')
    current_us_time = datetime.now(us_eastern)
    current_date = current_us_time.strftime('%Y-%m-%d')
    current_datetime = current_us_time.strftime('%Y-%m-%d %H:%M:%S EST')

    # 启用Google搜索功能 - 使用gemini-2.5-pro
    model = genai.GenerativeModel('gemini-2.5-pro')

    prompt = f"""
# **角色与使命**
你是一位顶尖的美股分析师，专精于T+0日内交易和波段操作。你的核心优势是**结合实时网络信息（新闻、财报、分析师评级）和量化异常信号**，提供具有前瞻性的交易策略。

# **重要时间信息**
- 当前美国东部时间: {current_datetime}
- 分析日期: {current_date}
- **请确保搜索和分析的是过去24小时内的最新信息**

# **任务指令**
1.  **信息分析要求:**
    - 基于你的训练数据中关于{stock_code}的最新可用信息进行分析
    - 重点关注该股票的历史表现模式、技术特征和基本面因素
    - 如果训练数据中包含近期信息，请重点分析；如果没有，请明确说明数据截止时间
    - 建议用户查看实时财经新闻网站获取{current_date}的最新信息
2.  **数据解读:** 结合你的知识库信息和以下量化数据进行专业分析。
3.  **当日T+0策略:** 你的建议必须针对{current_date}这一交易日，充分考虑美股T+0和无涨跌幅限制的特性。

# **输入信息（Raw Data）**
- 股票代码: {stock_code}
- 宏观趋势（量化）: {macro_analysis.get('trend', 'N/A')}
- 关键价位（量化）: 支撑 {macro_analysis.get('support', 'N/A')}, 阻力 {macro_analysis.get('resistance', 'N/A')}
- 市场异常特征（量化）: {json.dumps(abnormal_features, indent=2)}

# **输出格式要求**
**【{stock_code} (US Market) - {current_date} 当日深度分析】**
**🌐 过去24小时实时信息摘要:**
- 24小时内突发新闻: [基于搜索结果，总结过去24小时内影响该股票的关键新闻或事件]
- 盘前/盘后动态: [基于搜索结果，总结盘前盘后交易情况和重要信息]
- 最新评级/业绩变化: [基于搜索结果，总结过去24小时内的分析师评级或业绩指引变化]
- 当前股价状况: [基于搜索结果，说明当前股价水平和最新表现]
**🧠 量化行为解码:**
- 主力/机构意图: [结合量化特征和新闻，判断机构行为]
- 市场情绪: [判断当前市场是risk-on还是risk-off]
**⚡ 推演与预警:**
- 短期关键驱动力: [指出接下来影响股价的核心因素是什么]
- 潜在风险: [基于T+0特性，指出可能的日内大幅波动风险]
**🎯 {current_date} 当日T+0操作建议:**
- 当日核心策略: [基于过去24小时信息，明确建议今日做多、做空或观望]
- 当日关键入场/出场位: [提供具体的价格区间用于今日交易]
- 当日止损策略: [给出严格的止损建议，考虑今日可能的波动]
- 当日重点观测: [指出今日需要特别关注的时间点、新闻发布或价格行为]
- 风险提醒: [基于过去24小时信息，提醒今日可能的突发风险]
"""
    try:
        # 由于搜索功能可能不可用，使用优化的prompt来获取最佳分析
        enhanced_prompt = f"""
{prompt}

**重要提醒：**
- 请基于你的训练数据中关于{stock_code}的最新可用信息进行分析
- 如果你的训练数据中没有最新的24小时信息，请明确说明数据截止时间
- 提供基于历史模式和技术分析的专业判断
- 建议用户查看实时财经新闻网站（如Bloomberg、Reuters、Yahoo Finance）获取最新信息
- 重点关注技术分析和量化指标的解读
"""

        response = model.generate_content(enhanced_prompt)
        return response.text
    except Exception as e:
        return f"生成Gemini报告时出错: {e}"

def generate_report(stock_code: str, macro_analysis: dict, abnormal_features: dict) -> str:
    """
    总入口，根据股票市场选择合适的AI模型生成报告。
    """
    if _is_us_stock(stock_code):
        print("检测到美股，使用Gemini Pro进行分析...")
        # For Gemini, we don't need micro_analysis, but the function signature is kept for consistency
        return generate_gemini_report(stock_code, macro_analysis, abnormal_features)
    else:
        print("检测到A股，使用Qwen-Max进行分析...")
        # The original function expected micro_analysis, let's pass an empty dict for now
        return generate_qwen_report(stock_code, macro_analysis, abnormal_features)
