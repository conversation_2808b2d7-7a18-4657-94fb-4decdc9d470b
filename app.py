import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots

import config
import data_fetcher
import macro_analysis
import feature_extractor
import reporter

def plot_stock_charts(daily_data: pd.DataFrame, macro_result: dict, stock_code: str):
    """
    使用Plotly绘制K线图和交易量图。
    """
    fig = make_subplots(rows=2, cols=1, shared_xaxes=True, 
                        vertical_spacing=0.03, subplot_titles=(f'{stock_code} K线图', '成交量'), 
                        row_heights=[0.7, 0.3])

    fig.add_trace(go.Candlestick(x=daily_data.index,
                               open=daily_data['open'], high=daily_data['high'],
                               low=daily_data['low'], close=daily_data['close'],
                               name='K线'), 
                  row=1, col=1)
    
    # Ensure MA columns exist before plotting
    if 'ma_20' in daily_data.columns:
        fig.add_trace(go.Scatter(x=daily_data.index, y=daily_data['ma_20'], mode='lines', name='MA20', line=dict(color='yellow')), row=1, col=1)
    if 'ma_60' in daily_data.columns:
        fig.add_trace(go.Scatter(x=daily_data.index, y=daily_data['ma_60'], mode='lines', name='MA60', line=dict(color='purple')), row=1, col=1)

    fig.add_hline(y=macro_result['support'], line_dash="dash", line_color="green", annotation_text=f"支撑位 {macro_result['support']:.2f}", row=1, col=1)
    fig.add_hline(y=macro_result['resistance'], line_dash="dash", line_color="red", annotation_text=f"阻力位 {macro_result['resistance']:.2f}", row=1, col=1)

    colors = ['green' if row['close'] >= row['open'] else 'red' for index, row in daily_data.iterrows()]
    fig.add_trace(go.Bar(x=daily_data.index, y=daily_data['volume'], name='成交量', marker_color=colors), row=2, col=1)

    fig.update_layout(
        title=f'宏观趋势分析',
        xaxis_rangeslider_visible=False,
        height=600
    )
    
    return fig

def main():
    st.set_page_config(page_title="跨市场深度行为分析系统", layout="wide")
    st.title("🌐 跨市场深度行为分析系统 (A股 & 美股)")
    st.caption("结合量化异常与AI联网分析，解码市场行为")

    st.sidebar.header("输入参数")
    stock_code = st.sidebar.text_input("输入股票代码 (例如: 000001.SZ 或 AAPL)", value="AAPL")

    # 添加最新信息输入框
    st.sidebar.subheader("📰 最新24小时信息 (可选)")
    latest_news = st.sidebar.text_area(
        "请输入该股票过去24小时的最新信息：",
        placeholder="例如：\n- 公司发布Q3财报，营收超预期\n- 分析师上调目标价至$200\n- 盘前交易上涨2.5%\n- CEO在会议上宣布新产品计划\n...",
        height=150,
        help="输入任何您了解的关于该股票的最新新闻、财报、分析师评级、盘前盘后交易情况等信息。AI将结合这些信息进行更准确的分析。"
    )

    if st.sidebar.button("开始深度分析"):
        if not stock_code:
            st.error("请输入有效的股票代码。")
            return

        stock_code = stock_code.strip().upper()

        with st.spinner(f'正在分析 {stock_code}，请稍候...'):
            # 1. Data Fetching
            daily_data = data_fetcher.get_daily_data(stock_code)
            minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=7)

            if daily_data.empty or minute_data.empty:
                st.error("无法获取到足够的股票数据，请检查代码或稍后再试。")
                return
            
            # Use datetime index for plotting
            if 'date' in daily_data.columns:
                daily_data.set_index('date', inplace=True)
            
            st.success(f"成功获取 {stock_code} 的日线和分钟线数据。")

            # 2. Analysis
            st.subheader("量化分析结果")
            daily_data_with_ma = macro_analysis.calculate_ma(daily_data.copy())
            macro_result = macro_analysis.analyze_macro(daily_data_with_ma)
            abnormal_features = feature_extractor.extract_abnormal_features(daily_data, minute_data)
            
            if "error" in abnormal_features:
                st.error(f"提取异常特征时出错: {abnormal_features['error']}")
                return

            col1, col2 = st.columns([2, 1])
            with col1:
                st.plotly_chart(plot_stock_charts(daily_data_with_ma, macro_result, stock_code), use_container_width=True)
            with col2:
                st.info(f"**宏观趋势:** {macro_result['trend']}")
                st.success("已提取5个维度的市场异常特征。")
                with st.expander("查看异常特征原始数据"):
                    st.json(abnormal_features)

            # 3. Generate Report
            st.subheader("🤖 AI 深度分析报告")
            report_placeholder = st.empty()

            if latest_news.strip():
                report_placeholder.info("AI分析师正在结合量化数据与您提供的最新信息，生成深度洞察...")
            else:
                report_placeholder.info("AI分析师正在基于量化数据和历史模式，生成深度洞察...")

            final_report = reporter.generate_report(stock_code, macro_result, abnormal_features, latest_news)
            report_placeholder.markdown(final_report)

if __name__ == "__main__":
    main()
