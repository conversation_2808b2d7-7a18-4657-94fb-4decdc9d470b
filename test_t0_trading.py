#!/usr/bin/env python3
"""
测试T+0日内交易分析系统
"""

import config
import reporter
import data_fetcher
import macro_analysis
import feature_extractor

def test_t0_trading_analysis():
    """
    测试T+0日内交易分析
    """
    stock_code = "TSLA"
    
    print(f"=== {stock_code} T+0日内交易分析 ===")
    
    # 模拟当日最新信息
    latest_news = """
- 特斯拉盘前公布Q3交付数据，达到48.5万辆，超出市场预期的47万辆
- 马斯克推特确认Cybertruck将在本月底开始小批量交付
- 摩根士丹利分析师上调目标价至$380，重申增持评级
- 盘前交易上涨1.8%，成交量比平时增加25%
- 中国市场10月销量数据显示环比增长15%
- 能源存储业务获得新的大型订单，价值约5亿美元
- 今日无重大负面消息，整体消息面偏正面
"""
    
    # 获取数据
    print("正在获取数据...")
    daily_data = data_fetcher.get_daily_data(stock_code, days=180)
    minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=7)
    
    if daily_data.empty or minute_data.empty:
        print("❌ 无法获取数据")
        return
    
    # T+0分析
    print("正在进行T+0分析...")
    macro_result = macro_analysis.analyze_macro(daily_data)
    abnormal_features = feature_extractor.extract_abnormal_features(daily_data, minute_data)
    
    # 显示关键数据
    print(f"\n📊 T+0交易关键数据:")
    print(f"当前价格: ${macro_result.get('current_price', 'N/A')}")
    print(f"预测区间: ${macro_result.get('predicted_low', 'N/A')} - ${macro_result.get('predicted_high', 'N/A')}")
    
    buy_zone = macro_result.get('buy_zone', ['N/A', 'N/A'])
    sell_zone = macro_result.get('sell_zone', ['N/A', 'N/A'])
    print(f"买入区间: ${buy_zone[0]} - ${buy_zone[1]}")
    print(f"卖出区间: ${sell_zone[0]} - ${sell_zone[1]}")
    print(f"预期波动: ${macro_result.get('expected_range', 'N/A')}")
    
    # 生成AI报告
    print("\n🤖 正在生成T+0交易策略...")
    try:
        report = reporter.generate_gemini_report(stock_code, macro_result, abnormal_features, latest_news)
        
        print("\n" + "="*80)
        print("T+0日内交易策略报告")
        print("="*80)
        print(report)
        print("="*80)
        
        # 保存报告
        filename = f"t0_trading_strategy_{stock_code}.md"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n✅ T+0交易策略已保存到 {filename}")
        
    except Exception as e:
        print(f"❌ 生成报告时出错: {e}")

def test_without_news():
    """
    测试没有最新消息时的情况
    """
    stock_code = "TSLA"
    
    print(f"\n=== 测试无最新消息情况 ===")
    
    daily_data = data_fetcher.get_daily_data(stock_code, days=180)
    minute_data = data_fetcher.get_minute_data(stock_code, period='15', days=7)
    
    if daily_data.empty or minute_data.empty:
        return
    
    macro_result = macro_analysis.analyze_macro(daily_data)
    abnormal_features = feature_extractor.extract_abnormal_features(daily_data, minute_data)
    
    try:
        report = reporter.generate_gemini_report(stock_code, macro_result, abnormal_features, "")
        print("无消息时的回复:")
        print("-" * 50)
        print(report[:500] + "..." if len(report) > 500 else report)
        print("-" * 50)
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    if not config.GEMINI_API_KEY:
        print("❌ 未配置GEMINI_API_KEY")
    else:
        # 测试有消息的情况
        test_t0_trading_analysis()
        
        # 测试无消息的情况
        test_without_news()
