#!/usr/bin/env python3
"""
简单测试Gemini搜索功能
"""

import google.generativeai as genai
import config

def test_simple_gemini_search():
    """
    简单测试Gemini的搜索功能
    """
    if not config.GEMINI_API_KEY:
        print("错误：未配置GEMINI_API_KEY")
        return
    
    genai.configure(api_key=config.GEMINI_API_KEY)
    
    # 测试不同的模型和配置
    models_to_test = [
        'gemini-2.5-pro',
        'gemini-2.5-flash',
        'gemini-1.5-pro'
    ]
    
    for model_name in models_to_test:
        print(f"\n=== 测试模型: {model_name} ===")
        
        try:
            model = genai.GenerativeModel(model_name)
            
            # 简单的搜索测试
            prompt = "Search for the latest news about Apple stock (AAPL) in the past 24 hours"
            
            print("尝试使用google_search_retrieval...")
            try:
                response = model.generate_content(
                    prompt,
                    tools='google_search_retrieval'
                )
                print("✓ google_search_retrieval 成功!")
                print(f"响应长度: {len(response.text)} 字符")
                print(f"响应预览: {response.text[:200]}...")
                break
            except Exception as e:
                print(f"✗ google_search_retrieval 失败: {e}")
            
            # 尝试不同的工具配置
            print("尝试使用tools=[{'google_search': {}}]...")
            try:
                response = model.generate_content(
                    prompt,
                    tools=[{'google_search': {}}]
                )
                print("✓ google_search 成功!")
                print(f"响应长度: {len(response.text)} 字符")
                print(f"响应预览: {response.text[:200]}...")
                break
            except Exception as e:
                print(f"✗ google_search 失败: {e}")
            
            # 尝试无搜索功能
            print("尝试无搜索功能的普通调用...")
            try:
                response = model.generate_content(prompt)
                print("✓ 普通调用成功!")
                print(f"响应长度: {len(response.text)} 字符")
                print(f"响应预览: {response.text[:200]}...")
            except Exception as e:
                print(f"✗ 普通调用失败: {e}")
                
        except Exception as e:
            print(f"✗ 模型初始化失败: {e}")

if __name__ == "__main__":
    test_simple_gemini_search()
